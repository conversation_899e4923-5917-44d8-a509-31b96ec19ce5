// متغيرات عامة - استخدام config.js والحالة العامة
let API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || "http://localhost:5500/api");
let extraHours = window.GlobalState ? window.GlobalState.extraHours : [];
let employees = window.GlobalState ? window.GlobalState.employees : [];
let departments = window.GlobalState ? window.GlobalState.departments : [];
let currentEditId = window.GlobalState ? window.GlobalState.currentEditId : null;



// عناصر DOM - سيتم تحديدها بعد تحميل DOM
let extraHourForm, employeeSearchAdd, employeeCode, employeeName, employeeDepartment, employeeJob;
let extraHoursInput, extraDate, notes;
let extraHoursTable, extraHoursTableBody;
let filterStartDate, filterEndDate, filterDepartment, filterEmployee;
let applyFiltersBtn, clearFiltersBtn, reportsTable, reportsTableBody;
let totalRecords, totalHours;
let editEmployeeSearch, editEmployeeCode, editEmployeeName, editEmployeeDepartment;
let editExtraHours, editExtraDate, editNotes;

// تحديد عناصر DOM
function initDOMElements() {
  // عناصر نموذج الإضافة
  extraHourForm = document.getElementById('extraHourForm');
  employeeSearchAdd = document.getElementById('employeeSearchAdd');
  employeeCode = document.getElementById('employeeCode');
  employeeName = document.getElementById('employeeName');
  employeeDepartment = document.getElementById('employeeDepartment');
  employeeJob = document.getElementById('employeeJob');
  extraHoursInput = document.getElementById('extraHours');
  extraDate = document.getElementById('extraDate');
  notes = document.getElementById('notes');

  // عناصر الجدول
  extraHoursTable = document.getElementById('extraHoursTable');
  extraHoursTableBody = document.getElementById('extraHoursTableBody');

  // عناصر التقارير
  filterStartDate = document.getElementById('filterStartDate');
  filterEndDate = document.getElementById('filterEndDate');
  filterDepartment = document.getElementById('filterDepartment');
  filterEmployee = document.getElementById('filterEmployee');
  applyFiltersBtn = document.getElementById('applyFilters');
  clearFiltersBtn = document.getElementById('clearFilters');
  reportsTable = document.getElementById('reportsTable');
  reportsTableBody = document.getElementById('reportsTableBody');

  // عناصر الإحصائيات
  totalRecords = document.getElementById('totalRecords');
  totalHours = document.getElementById('totalHours');

  // عناصر نموذج التعديل
  editEmployeeSearch = document.getElementById('editEmployeeSearch');
  editEmployeeCode = document.getElementById('editEmployeeCode');
  editEmployeeName = document.getElementById('editEmployeeName');
  editEmployeeDepartment = document.getElementById('editEmployeeDepartment');
  editExtraHours = document.getElementById('editExtraHours');
  editExtraDate = document.getElementById('editExtraDate');
  editNotes = document.getElementById('editNotes');


}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // تحديد عناصر DOM أولاً
  initDOMElements();

  // تحقق من الصلاحيات
  checkPermissions();

  // التحقق من المحتوى المحدد من البطاقات
  checkSelectedContent();

  // تحميل البيانات
  loadEmployees();
  loadExtraHours();

  // تهيئة الأحداث
  initEventListeners();

  // إعداد فلاتر الساعات الإضافية
  setupExtraHoursFilters();

  // تعيين التاريخ الحالي
  if (extraDate) {
    const today = new Date().toISOString().split('T')[0];
    extraDate.value = today;
  }

  // تهيئة القائمة الجانبية
  setTimeout(() => {
    if (typeof SidebarManager !== 'undefined') {
      const sidebarManager = new SidebarManager();
      sidebarManager.init();
    }
  }, 100);
});

// تحقق من الصلاحيات
function checkPermissions() {
  const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');

  if (!permissions.view_extra_hours) {
    alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
    window.location.href = 'dashboard.html';
    return;
  }

  // التحقق من الصلاحيات للمحتوى المحدد
  const selectedContent = localStorage.getItem('selectedExtraHoursTab');

  if (selectedContent === 'add-extra' && !permissions.add_extra_hour) {
    alert('ليس لديك صلاحية لإضافة عمل إضافي');
    window.location.href = 'extra-hours-cards.html';
    return;
  }

  if (selectedContent === 'reports' && !permissions.view_extra_hour_reports) {
    alert('ليس لديك صلاحية لعرض تقارير الإضافي');
    window.location.href = 'extra-hours-cards.html';
    return;
  }
}

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  const selectedContent = localStorage.getItem('selectedExtraHoursTab');

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedExtraHoursTab');

    // عرض المحتوى المناسب
    showContent(selectedContent);
  } else {
    // عرض المحتوى الافتراضي (إضافة عمل إضافي)
    showContent('add-extra');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-extra') {
        pageTitle.textContent = 'إضافة عمل إضافي';
      } else if (contentType === 'reports') {
        pageTitle.textContent = 'تقارير الإضافي';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'reports') {
      loadReports();
    }
  }
}

// تهيئة مستمعي الأحداث
function initEventListeners() {
  // نموذج الإضافة
  extraHourForm.addEventListener('submit', handleFormSubmit);
  
  // البحث عن الموظف
  if (employeeSearchAdd) {
    employeeSearchAdd.addEventListener('input', function() {
      handleEmployeeSearch(this.value, 'employeeSearchSuggestions');
    });
    employeeSearchAdd.addEventListener('change', function() {
      handleEmployeeSelection(this.value);
    });
  }

  if (editEmployeeSearch) {
    editEmployeeSearch.addEventListener('input', function() {
      handleEmployeeSearch(this.value, 'editEmployeeSuggestions');
    });
    editEmployeeSearch.addEventListener('change', function() {
      handleEditEmployeeSelection(this.value);
    });
  }
  
  // فلاتر التقارير
  if (document.getElementById('generateReport')) {
    document.getElementById('generateReport').addEventListener('click', generateReport);
  }
  if (document.getElementById('exportReportBtn')) {
    document.getElementById('exportReportBtn').addEventListener('click', exportToExcel);
  }
}

// تحميل بيانات الموظفين
async function loadEmployees() {
  try {
    // إضافة معامل لاستبعاد الموظفين المستقيلين
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees?include_resigned=false`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();
      populateDepartmentFilter();
    } else {
      throw new Error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    // في حالة الخطأ، يمكن تحميل بيانات تجريبية
    loadSampleEmployees();
  }
}

// تحميل بيانات احتياطية في حالة فشل الاتصال بالخادم
function loadSampleEmployees() {
  employees = [];
  showNotification('فشل في تحميل بيانات الموظفين. يرجى التحقق من الاتصال بالخادم.', 'error');
}

// تحميل بيانات الساعات الإضافية
async function loadExtraHours() {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/extra-hours`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      extraHours = await response.json();
      // ترتيب البيانات المحملة من الخادم حسب تاريخ الإنشاء (الأحدث أولاً)
      extraHours.sort((a, b) => {
        const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
        const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
        return bTime - aTime;
      });
    } else {
      // تحميل البيانات من localStorage
      loadExtraHoursFromLocalStorage();
    }
  } catch (error) {
    // تحميل البيانات من localStorage
    loadExtraHoursFromLocalStorage();
  }

  displayExtraHours();
}

// تحميل البيانات من localStorage
function loadExtraHoursFromLocalStorage() {
  try {
    const savedData = localStorage.getItem('extraHours');
    if (savedData) {
      extraHours = JSON.parse(savedData);
      // ترتيب البيانات المحملة من localStorage حسب تاريخ الإنشاء (الأحدث أولاً)
      extraHours.sort((a, b) => {
        const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
        const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
        return bTime - aTime;
      });
    } else {
      extraHours = [];
    }
  } catch (error) {
    extraHours = [];
  }
}

// عرض الساعات الإضافية في الجدول
function displayExtraHours() {
  extraHoursTableBody.innerHTML = '';

  if (extraHours.length === 0) {
    extraHoursTableBody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد بيانات</td></tr>';
    return;
  }

  // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً) ثم حسب ID
  const sortedExtraHours = [...extraHours].sort((a, b) => {
    // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
    const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
    const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
    return bTime - aTime; // الأحدث أولاً
  });

  // التحقق من الصلاحيات
  const canEdit = hasPermission('edit_extra_hour');
  const canDelete = hasPermission('delete_extra_hour');

  sortedExtraHours.forEach((extraHour, index) => {
    const row = document.createElement('tr');

    // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
    if (index === 0) {
      row.style.backgroundColor = '#e8f5e8';
      row.style.border = '2px solid #4CAF50';
    }

    // إنشاء أزرار الإجراءات حسب الصلاحيات
    let actionsHTML = '<div class="action-buttons">';

    if (canEdit) {
      actionsHTML += `<button class="edit-btn" onclick="editExtraHour(${extraHour.id})" title="تعديل">
        <i class="fas fa-edit"></i>
      </button>`;
    }

    if (canDelete) {
      actionsHTML += `<button class="delete-btn" onclick="deleteExtraHour(${extraHour.id})" title="حذف">
        <i class="fas fa-trash"></i>
      </button>`;
    }

    if (!canEdit && !canDelete) {
      actionsHTML += '<span class="no-actions">لا توجد إجراءات متاحة</span>';
    }

    actionsHTML += '</div>';

    row.innerHTML = `
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${extraHour.employee_code}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${extraHour.employee_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${extraHour.department}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatHours(extraHour.extra_hours)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(extraHour.extra_date)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${extraHour.notes || '-'}</td>
      <td>${actionsHTML}</td>
    `;
    extraHoursTableBody.appendChild(row);
  });
}

// تنسيق عدد الساعات
function formatHours(hours) {
  if (!hours) return '0';
  const num = parseFloat(hours);
  // إذا كان الرقم صحيح (بدون كسور) اعرضه بدون .00
  if (num % 1 === 0) {
    return num.toString();
  }
  // إذا كان له كسور اعرضه بالكسور
  return num.toFixed(1);
}

// استخدام دالة formatDate من shared-utils.js

// معالجة إرسال النموذج
async function handleFormSubmit(e) {
  e.preventDefault();
  console.log('تم استدعاء handleFormSubmit');

  const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
  if (!permissions.add_extra_hour) {
    showErrorMessage('ليس لديك صلاحية لإضافة ساعات إضافية');
    return;
  }

  // التحقق من صحة البيانات
  if (!employeeCode.value) {
    showErrorMessage('يرجى اختيار الموظف');
    return;
  }

  if (!extraHoursInput.value) {
    showErrorMessage('يرجى إدخال عدد الساعات الإضافية');
    return;
  }

  if (!extraDate.value) {
    showErrorMessage('يرجى تحديد تاريخ العمل الإضافي');
    return;
  }

  // التحقق من عدد الساعات
  const hoursValue = parseFloat(extraHoursInput.value) || 0;
  if (hoursValue <= 0) {
    alert('عدد الساعات يجب أن يكون أكبر من صفر');
    return;
  }

  try {
    // البحث عن بيانات الموظف المحدد
    const selectedEmployee = employees.find(emp => String(emp.code) === String(employeeCode.value));

    if (!selectedEmployee) {
      alert('خطأ: لم يتم العثور على بيانات الموظف المحدد');
      return;
    }

    // إعداد بيانات العمل الإضافي
    const extraHourData = {
      employee_code: employeeCode.value,
      employee_name: selectedEmployee.full_name,
      department: selectedEmployee.department,
      job_title: selectedEmployee.job_title || '',
      extra_hours: hoursValue,
      extra_date: extraDate.value,
      notes: notes.value || ''
    };



    // محاولة إرسال البيانات للخادم
    try {
      console.log('إرسال البيانات للخادم:', extraHourData);
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/extra-hours`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(extraHourData)
      });

      console.log('استجابة الخادم:', response.status, response.statusText);

      if (response.ok) {
        const responseData = await response.json();
        const newExtraHour = {
          id: responseData.id || Date.now(),
          created_at: new Date().toISOString(),
          ...extraHourData
        };

        // إضافة العنصر الجديد في بداية المصفوفة ليظهر في أول الجدول
        extraHours.unshift(newExtraHour);
        showSuccessMessage('تم حفظ العمل الإضافي بنجاح');
        clearForm();
        displayExtraHours();
        return;
      } else if (response.status === 409) {
        // حالة التكرار
        const errorData = await response.json();
        if (errorData.duplicate) {
          showErrorMessage(`تنبيه: ${errorData.error}\nالتاريخ: ${formatDate(errorData.existing_date)}`);
          return;
        }
      } else {
        // أخطاء أخرى من الخادم
        const errorData = await response.json();
        showErrorMessage(errorData.error || 'حدث خطأ أثناء حفظ البيانات');
        return;
      }
    } catch (serverError) {
      // خطأ في الاتصال بالخادم، سيتم الحفظ محلياً
    }

    // في حالة فشل الخادم، احفظ البيانات محلياً
    const newExtraHour = {
      id: Date.now(),
      created_at: new Date().toISOString(),
      ...extraHourData
    };

    // إضافة العنصر الجديد في بداية المصفوفة ليظهر في أول الجدول
    extraHours.unshift(newExtraHour);

    // حفظ البيانات في localStorage
    localStorage.setItem('extraHours', JSON.stringify(extraHours));

    alert('تم حفظ العمل الإضافي بنجاح (محلياً)');
    clearForm();
    displayExtraHours();

  } catch (error) {
    alert('حدث خطأ في إضافة العمل الإضافي');
  }
}

// مسح النموذج
function clearForm() {
  extraHourForm.reset();
  employeeSearchAdd.value = '';
  employeeCode.value = '';
  employeeName.value = '';
  employeeDepartment.value = '';
  if (employeeJob) employeeJob.value = '';

  // تعيين التاريخ الحالي
  const today = new Date().toISOString().split('T')[0];
  extraDate.value = today;
}

// حذف العمل الإضافي
async function deleteExtraHour(id) {
  if (!confirm('هل أنت متأكد من حذف هذا السجل؟')) {
    return;
  }

  const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
  if (!permissions.delete_extra_hour) {
    alert('ليس لديك صلاحية لحذف الساعات الإضافية');
    return;
  }

  try {
    // محاولة حذف من الخادم
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/extra-hours/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // تم حذف السجل من الخادم أو سيتم الحذف محلياً
    } catch (serverError) {
      // خطأ في الاتصال بالخادم، سيتم الحذف محلياً
    }

    // حذف من البيانات المحلية
    extraHours = extraHours.filter(item => item.id != id);

    // حفظ البيانات المحدثة في localStorage
    localStorage.setItem('extraHours', JSON.stringify(extraHours));

    alert('تم حذف السجل بنجاح');
    displayExtraHours();

  } catch (error) {
    alert('حدث خطأ في حذف السجل');
  }
}

// البحث عن الموظف
function handleEmployeeSearch(searchTerm, datalistId) {
  const datalist = document.getElementById(datalistId);
  datalist.innerHTML = '';



  if (!searchTerm || searchTerm.trim() === '') {
    // مسح الحقول عند مسح البحث
    if (datalistId === 'employeeSearchSuggestions') {
      employeeCode.value = '';
      employeeName.value = '';
      employeeDepartment.value = '';
      if (employeeJob) employeeJob.value = '';
    } else if (datalistId === 'editEmployeeSuggestions') {
      editEmployeeCode.value = '';
      editEmployeeName.value = '';
      editEmployeeDepartment.value = '';
    }
    return;
  }

  const searchTermLower = searchTerm.toLowerCase().trim();

  // البحث في الموظفين - تحسين البحث ليشمل أجزاء من الاسم
  const filteredEmployees = employees.filter(emp => {
    // التأكد من أن البيانات موجودة وتحويلها إلى نص
    const fullName = emp.full_name ? String(emp.full_name).toLowerCase() : '';
    const code = emp.code ? String(emp.code).toLowerCase() : '';
    const department = emp.department ? String(emp.department).toLowerCase() : '';

    // البحث في كل الحقول
    return fullName.includes(searchTermLower) ||
           code.includes(searchTermLower) ||
           department.includes(searchTermLower);
  });



  // البحث عن تطابق مباشر أولاً
  const exactCodeMatch = employees.find(emp =>
    emp.code && String(emp.code).toLowerCase() === searchTermLower
  );
  const exactNameMatch = employees.find(emp =>
    emp.full_name && String(emp.full_name).toLowerCase() === searchTermLower
  );

  const isEdit = datalistId === 'editEmployeeSuggestions';

  // إذا وُجد تطابق مباشر، املأ الحقول
  if (exactCodeMatch) {
    fillEmployeeFieldsLocal(exactCodeMatch, isEdit);
  } else if (exactNameMatch) {
    fillEmployeeFieldsLocal(exactNameMatch, isEdit);
  }
  // إذا كان هناك موظف واحد فقط في النتائج، املأ الحقول
  else if (filteredEmployees.length === 1) {
    fillEmployeeFieldsLocal(filteredEmployees[0], isEdit);
  }
  // إذا كان البحث يبدأ بكود أو اسم موظف، املأ الحقول
  else {
    const startsWithMatch = employees.find(emp => {
      const nameLower = emp.full_name ? String(emp.full_name).toLowerCase() : '';
      const codeLower = emp.code ? String(emp.code).toLowerCase() : '';
      return nameLower.startsWith(searchTermLower) || codeLower.startsWith(searchTermLower);
    });

    if (startsWithMatch) {
      fillEmployeeFieldsLocal(startsWithMatch, isEdit);
    }
  }

  // إضافة الخيارات للقائمة المنسدلة
  if (filteredEmployees.length > 0) {
    // ترتيب النتائج: الأكواد أولاً ثم الأسماء
    filteredEmployees.sort((a, b) => {
      const aCodeMatch = a.code && String(a.code).toLowerCase().includes(searchTermLower);
      const bCodeMatch = b.code && String(b.code).toLowerCase().includes(searchTermLower);

      if (aCodeMatch && !bCodeMatch) return -1;
      if (!aCodeMatch && bCodeMatch) return 1;

      return String(a.full_name || '').localeCompare(String(b.full_name || ''));
    });

    // عرض أول 15 نتيجة فقط لتحسين الأداء
    filteredEmployees.slice(0, 15).forEach(emp => {
      const option = document.createElement('option');
      option.value = `${emp.code} - ${emp.full_name}`;
      datalist.appendChild(option);
    });


  }
}

// استخدام دالة fillEmployeeFields من shared-utils.js
// دالة محلية للتوافق مع الكود الحالي
function fillEmployeeFieldsLocal(employee, isEdit = false) {
  if (employee) {
    if (isEdit) {
      editEmployeeCode.value = employee.code || '';
      editEmployeeName.value = employee.full_name || '';
      editEmployeeDepartment.value = employee.department || '';
    } else {
      employeeCode.value = employee.code || '';
      employeeName.value = employee.full_name || '';
      employeeDepartment.value = employee.department || '';
      if (employeeJob) employeeJob.value = employee.job_title || '';
    }
  }
}

// اختيار الموظف
function handleEmployeeSelection(value) {
  if (!value) return;

  const codeMatch = value.match(/^(\S+)\s*-/);
  if (!codeMatch) return;

  const code = codeMatch[1];
  const employee = employees.find(emp => emp.code == code);

  if (employee) {
    fillEmployeeFieldsLocal(employee);
  }
}

// اختيار الموظف في نموذج التعديل
function handleEditEmployeeSelection(value) {
  if (!value) return;

  const codeMatch = value.match(/^(\S+)\s*-/);
  if (!codeMatch) return;

  const code = codeMatch[1];
  const employee = employees.find(emp => emp.code == code);

  if (employee) {
    editEmployeeCode.value = employee.code || '';
    editEmployeeName.value = employee.full_name || '';
    editEmployeeDepartment.value = employee.department || '';
  }
}

// ملء فلتر الإدارات
function populateDepartmentFilter() {
  const departments = [...new Set(employees.map(emp => emp.department))].sort();
  
  filterDepartment.innerHTML = '<option value="">جميع الإدارات</option>';
  departments.forEach(dept => {
    const option = document.createElement('option');
    option.value = dept;
    option.textContent = dept;
    filterDepartment.appendChild(option);
  });
}

// تعديل عمل إضافي
function editExtraHour(id) {
  const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
  if (!permissions.edit_extra_hour) {
    alert('ليس لديك صلاحية لتعديل الساعات الإضافية');
    return;
  }
  
  const extraHour = extraHours.find(eh => eh.id === id);
  if (!extraHour) {
    alert('السجل غير موجود');
    return;
  }
  
  currentEditId = id;
  
  // ملء النموذج
  editEmployeeSearch.value = `${extraHour.employee_code} - ${extraHour.employee_name}`;
  editEmployeeCode.value = extraHour.employee_code;
  editEmployeeName.value = extraHour.employee_name;
  editEmployeeDepartment.value = extraHour.department;
  editExtraHours.value = extraHour.extra_hours;
  editNotes.value = extraHour.notes || '';
  
  // إصلاح مشكلة التاريخ الناقص يوم
  if (editExtraDate) {
    const date = new Date(extraHour.extra_date);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    editExtraDate.value = `${year}-${month}-${day}`;
  }
  
  // إظهار النافذة المنبثقة
  document.getElementById('editExtraHourModal').style.display = 'block';
}

// تحديث عمل إضافي
async function updateExtraHour() {
  if (!currentEditId) return;
  
  const formData = {
    employee_code: editEmployeeCode.value,
    extra_hours: parseFloat(editExtraHours.value),
    extra_date: editExtraDate.value,
    notes: editNotes.value
  };
  
  // التحقق من البيانات
  if (!formData.employee_code || !formData.extra_hours || !formData.extra_date) {
    alert('يرجى ملء جميع الحقول المطلوبة');
    return;
  }
  
  if (formData.extra_hours <= 0) {
    alert('عدد الساعات يجب أن يكون أكبر من صفر');
    return;
  }
  
  try {
    // البحث عن السجل في البيانات المحلية
    const extraHourIndex = extraHours.findIndex(eh => eh.id == currentEditId);
    if (extraHourIndex === -1) {
      alert('السجل غير موجود');
      return;
    }

    // تحديث البيانات
    const updatedData = {
      ...extraHours[extraHourIndex],
      extra_hours: formData.extra_hours,
      extra_date: formData.extra_date,
      notes: formData.notes || ''
    };

    // محاولة تحديث في الخادم
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/extra-hours/${currentEditId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updatedData)
      });

      // تم تحديث السجل في الخادم أو سيتم التحديث محلياً
    } catch (serverError) {
      // خطأ في الاتصال بالخادم، سيتم التحديث محلياً
    }

    // تحديث البيانات المحلية
    extraHours[extraHourIndex] = updatedData;

    // حفظ البيانات المحدثة في localStorage
    localStorage.setItem('extraHours', JSON.stringify(extraHours));

    alert('تم تحديث العمل الإضافي بنجاح');
    closeEditModal();
    displayExtraHours();

  } catch (error) {
    alert('حدث خطأ في تحديث العمل الإضافي');
  }
}



// تحميل تقارير الساعات الإضافية
function loadReports() {
  displayReports(extraHours);
}

// عرض التقارير
function displayReports(reportsData) {
  reportsTableBody.innerHTML = '';

  if (reportsData.length === 0) {
    reportsTableBody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد بيانات</td></tr>';
    updateStatistics(0, 0);
    return;
  }

  let totalHoursValue = 0;

  // تجميع البيانات حسب الموظف لإنشاء أزرار التفاصيل
  const employeeGroups = {};
  reportsData.forEach(extraHour => {
    const key = `${extraHour.employee_code}-${extraHour.employee_name}`;
    if (!employeeGroups[key]) {
      employeeGroups[key] = {
        employee_code: extraHour.employee_code,
        employee_name: extraHour.employee_name,
        department: extraHour.department,
        records: []
      };
    }
    employeeGroups[key].records.push(extraHour);
  });

  // عرض البيانات مع إضافة زر التفاصيل للموظفين الذين لديهم أكثر من سجل واحد
  const processedEmployees = new Set();

  reportsData.forEach(extraHour => {
    const employeeKey = `${extraHour.employee_code}-${extraHour.employee_name}`;
    const row = document.createElement('tr');

    // إضافة زر التفاصيل فقط للسجل الأول من كل موظف إذا كان لديه أكثر من سجل
    let detailsButton = '';
    if (!processedEmployees.has(employeeKey) && employeeGroups[employeeKey].records.length > 1) {
      detailsButton = `<button class="details-btn" onclick="showEmployeeExtraHoursDetails('${extraHour.employee_code}', '${extraHour.employee_name}', '${extraHour.department}')">عرض التفاصيل</button>`;
      processedEmployees.add(employeeKey);
    } else if (!processedEmployees.has(employeeKey)) {
      detailsButton = '-';
      processedEmployees.add(employeeKey);
    } else {
      detailsButton = '';
    }

    row.innerHTML = `
      <td>${extraHour.employee_code}</td>
      <td>${extraHour.employee_name}</td>
      <td>${extraHour.department}</td>
      <td>${formatHours(extraHour.extra_hours)}</td>
      <td>${formatDate(extraHour.extra_date)}</td>
      <td>${extraHour.notes || '-'}</td>
      <td>${detailsButton}</td>
    `;
    reportsTableBody.appendChild(row);

    totalHoursValue += parseFloat(extraHour.extra_hours);
  });

  // تحديث الإحصائيات
  updateStatistics(reportsData.length, totalHoursValue);
}

// تحديث الإحصائيات
function updateStatistics(recordsCount, hoursCount) {
  if (totalRecords) totalRecords.textContent = recordsCount;
  if (totalHours) totalHours.textContent = formatHours(hoursCount);

  // حساب متوسط الساعات
  const avgHours = recordsCount > 0 ? hoursCount / recordsCount : 0;
  const avgHoursElement = document.getElementById('avgHours');
  if (avgHoursElement) {
    avgHoursElement.textContent = formatHours(avgHours);
  }
}

// إنشاء التقرير (نفس وظيفة applyFilters)
function generateReport() {
  applyFilters();
}

// تطبيق الفلاتر
function applyFilters() {
  let filteredExtraHours = [...extraHours];

  // فلتر التاريخ - إصلاح مشكلة النقص يوم
  if (filterStartDate.value) {
    const startDateFilter = new Date(filterStartDate.value);
    startDateFilter.setHours(0, 0, 0, 0);
    filteredExtraHours = filteredExtraHours.filter(extraHour => {
      const extraHourDate = new Date(extraHour.extra_date);
      extraHourDate.setHours(0, 0, 0, 0);
      return extraHourDate >= startDateFilter;
    });
  }

  if (filterEndDate.value) {
    const endDateFilter = new Date(filterEndDate.value);
    endDateFilter.setHours(23, 59, 59, 999);
    filteredExtraHours = filteredExtraHours.filter(extraHour => {
      const extraHourDate = new Date(extraHour.extra_date);
      extraHourDate.setHours(0, 0, 0, 0);
      return extraHourDate <= endDateFilter;
    });
  }

  // فلتر الإدارة
  if (filterDepartment.value) {
    filteredExtraHours = filteredExtraHours.filter(extraHour =>
      extraHour.department === filterDepartment.value
    );
  }

  // فلتر الموظف
  if (filterEmployee.value) {
    const searchTerm = filterEmployee.value.toLowerCase();
    filteredExtraHours = filteredExtraHours.filter(extraHour =>
      extraHour.employee_name.toLowerCase().includes(searchTerm) ||
      extraHour.employee_code.toString().includes(searchTerm)
    );
  }

  displayReports(filteredExtraHours);
}

// مسح الفلاتر
function clearFilters() {
  filterStartDate.value = '';
  filterEndDate.value = '';
  filterDepartment.value = '';
  filterEmployee.value = '';

  displayReports(extraHours);
}

// طباعة التقرير
function printReport() {
  const printWindow = window.open('', '_blank');
  const currentDate = new Date().toLocaleDateString('ar-SA');

  let tableRows = '';
  const tableBody = reportsTableBody;

  for (let i = 0; i < tableBody.rows.length; i++) {
    const row = tableBody.rows[i];
    tableRows += '<tr>';
    for (let j = 0; j < row.cells.length; j++) {
      tableRows += `<td>${row.cells[j].textContent}</td>`;
    }
    tableRows += '</tr>';
  }

  const printContent = `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>تقرير الساعات الإضافية</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; margin-bottom: 10px; }
        .header p { color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; color: #666; }
        .stats { margin: 20px 0; text-align: center; }
        .stats span { margin: 0 20px; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>تقرير الساعات الإضافية</h1>
        <p>تاريخ الطباعة: ${currentDate}</p>
      </div>

      <div class="stats">
        <span>إجمالي السجلات: ${totalRecords.textContent}</span>
        <span>إجمالي الساعات: ${totalHours.textContent}</span>
      </div>

      <table>
        <thead>
          <tr>
            <th>كود الموظف</th>
            <th>اسم الموظف</th>
            <th>الإدارة</th>
            <th>عدد الساعات</th>
            <th>تاريخ الإضافي</th>
            <th>ملاحظات</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>

      <div class="footer">
        <p>نظام إدارة الموظفين - تقرير الساعات الإضافية</p>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.print();
}

// تصدير إلى Excel
function exportToExcel() {
  const data = [];
  const tableBody = reportsTableBody;

  // إضافة العناوين
  data.push(['كود الموظف', 'اسم الموظف', 'الإدارة', 'عدد الساعات', 'تاريخ الإضافي', 'ملاحظات']);

  // إضافة البيانات
  for (let i = 0; i < tableBody.rows.length; i++) {
    const row = tableBody.rows[i];
    const rowData = [];
    for (let j = 0; j < row.cells.length - 1; j++) { // تجاهل عمود التفاصيل
      rowData.push(row.cells[j].textContent);
    }
    data.push(rowData);
  }

  // إنشاء ملف CSV
  let csvContent = '\uFEFF'; // BOM for UTF-8
  data.forEach(row => {
    csvContent += row.join(',') + '\n';
  });

  // تحميل الملف
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `تقرير_الساعات_الإضافية_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// دالة عرض تفاصيل الساعات الإضافية للموظف
function showEmployeeExtraHoursDetails(employeeCode, employeeName, employeeDepartment) {
  console.log('عرض تفاصيل الساعات الإضافية للموظف:', employeeCode, employeeName);

  // فلترة الساعات الإضافية للموظف المحدد
  const employeeExtraHours = extraHours.filter(extraHour =>
    extraHour.employee_code === employeeCode
  );

  if (employeeExtraHours.length === 0) {
    alert('لا توجد ساعات إضافية لهذا الموظف');
    return;
  }

  // حساب الإحصائيات
  const totalHours = employeeExtraHours.reduce((sum, record) => sum + parseFloat(record.extra_hours), 0);
  const recordsCount = employeeExtraHours.length;
  const averageHours = totalHours / recordsCount;

  // ملء معلومات الموظف
  document.getElementById('detailsEmployeeCode').textContent = employeeCode;
  document.getElementById('detailsEmployeeFullName').textContent = employeeName;
  document.getElementById('detailsEmployeeDepartment').textContent = employeeDepartment;

  // ملء الإحصائيات
  document.getElementById('detailsTotalHours').textContent = formatHours(totalHours);
  document.getElementById('detailsRecordsCount').textContent = recordsCount;
  document.getElementById('detailsAverageHours').textContent = formatHours(averageHours);

  // ملء جدول التفاصيل
  const detailsTableBody = document.getElementById('extraHoursDetailsTableBody');
  detailsTableBody.innerHTML = '';

  // ترتيب السجلات حسب التاريخ (الأحدث أولاً)
  const sortedRecords = employeeExtraHours.sort((a, b) => new Date(b.extra_date) - new Date(a.extra_date));

  sortedRecords.forEach(record => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${formatDate(record.extra_date)}</td>
      <td>${formatHours(record.extra_hours)}</td>
      <td>${record.notes || '-'}</td>
    `;
    detailsTableBody.appendChild(row);
  });

  // إظهار النافذة المنبثقة
  document.getElementById('extraHoursDetailsModal').style.display = 'block';

  // إعداد أحداث الأزرار
  setupDetailsModalEvents(employeeCode, employeeName, employeeDepartment, employeeExtraHours);
}

// إعداد أحداث النافذة المنبثقة للتفاصيل
function setupDetailsModalEvents(employeeCode, employeeName, employeeDepartment, employeeExtraHours) {
  const modal = document.getElementById('extraHoursDetailsModal');
  const closeBtn = modal.querySelector('.close-details-btn');
  const printBtn = document.getElementById('printExtraHoursDetails');
  const exportBtn = document.getElementById('exportExtraHoursDetailsToExcel');

  // إغلاق النافذة
  closeBtn.onclick = () => {
    modal.style.display = 'none';
  };

  // إغلاق النافذة عند النقر خارجها
  window.onclick = (event) => {
    if (event.target === modal) {
      modal.style.display = 'none';
    }
  };

  // طباعة التفاصيل
  printBtn.onclick = () => {
    printEmployeeExtraHoursDetails(employeeCode, employeeName, employeeDepartment, employeeExtraHours);
  };

  // تصدير التفاصيل إلى Excel
  exportBtn.onclick = () => {
    exportEmployeeExtraHoursDetailsToExcel(employeeCode, employeeName, employeeDepartment, employeeExtraHours);
  };
}

// طباعة تفاصيل الساعات الإضافية للموظف
function printEmployeeExtraHoursDetails(employeeCode, employeeName, employeeDepartment, employeeExtraHours) {
  const printWindow = window.open('', '_blank');
  const currentDate = new Date().toLocaleDateString('ar-SA');

  // حساب الإحصائيات
  const totalHours = employeeExtraHours.reduce((sum, record) => sum + parseFloat(record.extra_hours), 0);
  const recordsCount = employeeExtraHours.length;
  const averageHours = totalHours / recordsCount;

  // ترتيب السجلات حسب التاريخ
  const sortedRecords = employeeExtraHours.sort((a, b) => new Date(b.extra_date) - new Date(a.extra_date));

  let tableRows = '';
  sortedRecords.forEach(record => {
    tableRows += `
      <tr>
        <td>${formatDate(record.extra_date)}</td>
        <td>${formatHours(record.extra_hours)}</td>
        <td>${record.notes || '-'}</td>
      </tr>
    `;
  });

  const printContent = `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>تفاصيل الساعات الإضافية - ${employeeName}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .employee-info { background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .employee-info p { margin: 5px 0; font-weight: bold; }
        .stats { display: flex; justify-content: space-around; margin-bottom: 20px; }
        .stats span { background-color: #673AB7; color: white; padding: 10px 15px; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #673AB7; color: white; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>تفاصيل الساعات الإضافية</h1>
        <p>تاريخ الطباعة: ${currentDate}</p>
      </div>

      <div class="employee-info">
        <p>كود الموظف: ${employeeCode}</p>
        <p>اسم الموظف: ${employeeName}</p>
        <p>الإدارة: ${employeeDepartment}</p>
      </div>

      <div class="stats">
        <span>إجمالي الساعات: ${formatHours(totalHours)}</span>
        <span>عدد السجلات: ${recordsCount}</span>
        <span>متوسط الساعات: ${formatHours(averageHours)}</span>
      </div>

      <table>
        <thead>
          <tr>
            <th>التاريخ</th>
            <th>عدد الساعات</th>
            <th>الملاحظات</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>

      <div class="footer">
        <p>نظام إدارة الموظفين - تفاصيل الساعات الإضافية</p>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.print();
}

// تصدير تفاصيل الساعات الإضافية للموظف إلى Excel
function exportEmployeeExtraHoursDetailsToExcel(employeeCode, employeeName, employeeDepartment, employeeExtraHours) {
  const data = [];

  // إضافة معلومات الموظف
  data.push(['تفاصيل الساعات الإضافية']);
  data.push(['كود الموظف', employeeCode]);
  data.push(['اسم الموظف', employeeName]);
  data.push(['الإدارة', employeeDepartment]);
  data.push(['']); // سطر فارغ

  // حساب الإحصائيات
  const totalHours = employeeExtraHours.reduce((sum, record) => sum + parseFloat(record.extra_hours), 0);
  const recordsCount = employeeExtraHours.length;
  const averageHours = totalHours / recordsCount;

  data.push(['الإحصائيات']);
  data.push(['إجمالي الساعات', formatHours(totalHours)]);
  data.push(['عدد السجلات', recordsCount]);
  data.push(['متوسط الساعات', formatHours(averageHours)]);
  data.push(['']); // سطر فارغ

  // إضافة العناوين
  data.push(['التاريخ', 'عدد الساعات', 'الملاحظات']);

  // ترتيب السجلات حسب التاريخ
  const sortedRecords = employeeExtraHours.sort((a, b) => new Date(b.extra_date) - new Date(a.extra_date));

  // إضافة البيانات
  sortedRecords.forEach(record => {
    data.push([
      formatDate(record.extra_date),
      formatHours(record.extra_hours),
      record.notes || '-'
    ]);
  });

  // إنشاء ملف CSV
  let csvContent = '\uFEFF'; // BOM for UTF-8
  data.forEach(row => {
    csvContent += row.join(',') + '\n';
  });

  // تحميل الملف
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `تفاصيل_الساعات_الإضافية_${employeeName}_${new Date().toLocaleDateString('ar-SA')}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// إغلاق نافذة التعديل
function closeEditModal() {
  document.getElementById('editExtraHourModal').style.display = 'none';
  currentEditId = null;

  // مسح النموذج
  if (editEmployeeSearch) editEmployeeSearch.value = '';
  if (editEmployeeCode) editEmployeeCode.value = '';
  if (editEmployeeName) editEmployeeName.value = '';
  if (editEmployeeDepartment) editEmployeeDepartment.value = '';
  if (editExtraHours) editExtraHours.value = '';
  if (editExtraDate) editExtraDate.value = '';
  if (editNotes) editNotes.value = '';
}

// تسجيل الخروج
function logout() {
  localStorage.removeItem('permissions');
  localStorage.removeItem('currentUser');
  window.location.href = 'index.html';
}

// إعداد فلاتر الساعات الإضافية المضافة
function setupExtraHoursFilters() {
  const applyExtraFiltersBtn = document.getElementById('applyExtraFiltersBtn');
  const clearExtraFiltersBtn = document.getElementById('clearExtraFiltersBtn');

  if (applyExtraFiltersBtn) {
    applyExtraFiltersBtn.addEventListener('click', function() {
      applyExtraHoursFilters();
    });
  }

  if (clearExtraFiltersBtn) {
    clearExtraFiltersBtn.addEventListener('click', function() {
      clearExtraHoursFilters();
    });
  }

  // إضافة البحث الفوري أثناء الكتابة
  const filterInputs = ['filterExtraEmployeeCode', 'filterExtraEmployeeName', 'filterExtraFromDate', 'filterExtraToDate'];
  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
          applyExtraHoursFilters();
        }, 300);
      });
    }
  });
}

// تطبيق فلاتر الساعات الإضافية المضافة
function applyExtraHoursFilters() {
  const employeeCode = document.getElementById('filterExtraEmployeeCode').value.trim();
  const employeeName = document.getElementById('filterExtraEmployeeName').value.trim();
  const fromDate = document.getElementById('filterExtraFromDate').value;
  const toDate = document.getElementById('filterExtraToDate').value;

  const tableBody = document.getElementById('extraHoursTableBody');
  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return; // تجاهل الصفوف الفارغة

    // استخراج البيانات من الخلايا
    const rowEmployeeCode = cells[0]?.textContent?.trim() || '';
    const rowEmployeeName = cells[1]?.textContent?.trim() || '';
    const rowDate = cells[4]?.textContent?.trim() || ''; // تاريخ الإضافي في العمود الخامس

    let showRow = true;

    // فلترة بالكود
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من
    if (fromDate && rowDate) {
      try {
        const rowDateObj = new Date(rowDate.split('/').reverse().join('-')); // تحويل من dd/mm/yyyy إلى yyyy-mm-dd
        const fromDateObj = new Date(fromDate);
        if (rowDateObj < fromDateObj) {
          showRow = false;
        }
      } catch (e) {
        if (rowDate < fromDate) {
          showRow = false;
        }
      }
    }

    // فلترة بالتاريخ إلى
    if (toDate && rowDate) {
      try {
        const rowDateObj = new Date(rowDate.split('/').reverse().join('-')); // تحويل من dd/mm/yyyy إلى yyyy-mm-dd
        const toDateObj = new Date(toDate);
        if (rowDateObj > toDateObj) {
          showRow = false;
        }
      } catch (e) {
        if (rowDate > toDate) {
          showRow = false;
        }
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح فلاتر الساعات الإضافية المضافة
function clearExtraHoursFilters() {
  document.getElementById('filterExtraEmployeeCode').value = '';
  document.getElementById('filterExtraEmployeeName').value = '';
  document.getElementById('filterExtraFromDate').value = '';
  document.getElementById('filterExtraToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.getElementById('extraHoursTableBody');
  const rows = tableBody.querySelectorAll('tr');
  rows.forEach(row => {
    row.style.display = '';
  });
}

// دالة لعرض رسائل النجاح
function showSuccessMessage(message) {
  // إزالة أي رسائل سابقة
  removeExistingMessages();

  const messageDiv = document.createElement('div');
  messageDiv.className = 'success-message';
  messageDiv.innerHTML = `
    <div class="message-content">
      <i class="fas fa-check-circle"></i>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(messageDiv);

  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.parentNode.removeChild(messageDiv);
    }
  }, 3000);
}

// دالة لعرض رسائل الخطأ
function showErrorMessage(message) {
  // إزالة أي رسائل سابقة
  removeExistingMessages();

  const messageDiv = document.createElement('div');
  messageDiv.className = 'error-message';
  messageDiv.innerHTML = `
    <div class="message-content">
      <i class="fas fa-exclamation-triangle"></i>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(messageDiv);

  // إزالة الرسالة بعد 5 ثوان
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.parentNode.removeChild(messageDiv);
    }
  }, 5000);
}

// دالة لإزالة الرسائل الموجودة
function removeExistingMessages() {
  const existingMessages = document.querySelectorAll('.success-message, .error-message');
  existingMessages.forEach(msg => {
    if (msg.parentNode) {
      msg.parentNode.removeChild(msg);
    }
  });
}

// دالة لتنسيق التاريخ
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-EG');
}

// إضافة الدوال إلى النطاق العام للوصول إليها من HTML
window.showEmployeeExtraHoursDetails = showEmployeeExtraHoursDetails;