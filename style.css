
@import url('shared-styles.css');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: var(--background-color);
  min-height: 100vh;
  margin: 0;
  display: flex;
  direction: rtl;
  color: var(--text-color);
}

/* تم حذف جميع تنسيقات القائمة الجانبية - يتم التحكم بها من sidebar.css فقط */

/* Server Controls */
.server-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-btn {
  padding: 8px 15px;
  border-radius: 4px;
  font-weight: bold;
  cursor: default;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  color: #333;
}

.status-btn.connected {
  background-color: #4CAF50;
  color: white;
  border-color: #45a049;
}

.status-btn.disconnected {
  background-color: #f44336;
  color: white;
  border-color: #da190b;
}

.control-btn {
  padding: 8px 15px;
  border-radius: 4px;
  background-color: #2196F3;
  color: white;
  border: none;
  cursor: pointer;

}

.control-btn:hover {
  background-color: #1976D2;
}

.control-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Container Styles */
.container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

/* Actions Bar */
.actions-bar {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 20px;
}

#searchInput {
  flex: 1;
  padding: 8px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.action-btn {
  padding: 8px 16px;
  background-color: #1abc9c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

}

.action-btn:hover {
  background-color: #16a085;
}

/* تم نقل تنسيقات الجداول إلى shared-styles.css */

/* Action Buttons - تم نقل التنسيقات إلى shared-styles.css للتوحيد */
/* هذه التنسيقات محفوظة للتوافق مع الإصدارات القديمة */

/* Modal Styles - تم نقلها إلى shared-styles.css */

/* .close styles moved to shared-styles.css */

.server-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.server-form input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.server-form button {
  padding: 10px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.server-form button:hover {
  background-color: #45a049;
}

/* Delete All Button */
#deleteAllBtn {
  padding: 8px 15px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;

}

#deleteAllBtn:hover {
  background-color: #d32f2f;
}

/* Responsive Design */
@media (max-width: 768px) {
  /* تم حذف تنسيقات nav-container - يتم التحكم بها من sidebar.css */

  .server-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .actions-bar {
    flex-direction: column;
  }

  .search-filters {
    width: 100%;
  }

  .search-filters input,
  .search-filters select {
    width: 100%;
  }

  .form-container {
    padding: 16px;
  }

  .form-title {
    font-size: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .table-container {
    padding: 8px;
  }

  .table thead th,
  .table tbody td {
    font-size: 0.95rem;
    padding: 8px 4px;
  }
}

/* تنسيقات خاصة للكميات في جدول العهد */
.quantity-spent {
  color: #dc3545 !important;
  font-weight: bold !important;
  background-color: rgba(220, 53, 69, 0.1) !important;
  border-radius: 4px;
  padding: 4px 8px !important;
}

.quantity-remaining {
  color: #28a745 !important;
  font-weight: bold !important;
  background-color: rgba(40, 167, 69, 0.1) !important;
  border-radius: 4px;
  padding: 4px 8px !important;
}

/* تحسين الألوان في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .quantity-spent {
    color: #ff6b6b !important;
    background-color: rgba(255, 107, 107, 0.15) !important;
  }

  .quantity-remaining {
    color: #51cf66 !important;
    background-color: rgba(81, 207, 102, 0.15) !important;
  }
}


}



.search-container {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}

.search-filters input,
.search-filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.search-filters input {
  flex: 1;
  min-width: 300px;
}

.search-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;

}

.search-btn:hover {
  background-color: #0056b3;
}

/* تم نقل تنسيقات الأزرار إلى shared-styles.css */

.delete-all {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;

}

.delete-all:hover {
  background-color: #c82333;
}

/* تم نقل تنسيقات الأزرار الأساسية إلى shared-styles.css */

/* تنسيقات النماذج */
.form-container {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 4px 24px var(--shadow-color);
  padding: 32px 24px;
  max-width: 900px;
  margin: 0 auto;
}

.form-title {
  text-align: center;
  font-size: 2.2rem;
  color: var(--primary-color);
  margin-bottom: 30px;
  letter-spacing: 1px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 18px 24px;
  margin-bottom: 28px;
}

.form-input {
  padding: 12px 14px;
  border: 1.5px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background: var(--white);

  outline: none;
  width: 100%;
}

.form-input:focus {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

/* تنسيقات الجداول */
/* تم نقل تنسيقات الجداول إلى shared-styles.css */



/* تم إزالة الانتقالات لتحسين الأداء */

/* أنماط خاصة بالصلاحيات */
.permission-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.permission-hidden {
  display: none !important;
}

/* تنسيق فلاتر البحث المحددة */
.filtered-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.search-filters-row .form-group {
  display: flex;
  flex-direction: column;
}

.search-filters-row .form-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.filter-actions .reset-btn,
.filter-actions .export-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.filter-actions .reset-btn {
  background-color: #6c757d;
  color: white;
}

.filter-actions .reset-btn:hover {
  background-color: #5a6268;
}

.filter-actions .export-btn {
  background-color: #28a745;
  color: white;
}

.filter-actions .export-btn:hover {
  background-color: #218838;
}

/* تنسيق الصف المميز (آخر إضافة) لجدول التدريب */
#training-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

#training-table tr:first-child td {
  font-weight: bold;
}

/* تنسيقات النافذة المنبثقة العامة */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow-y: auto;
}

.modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 25px;
  border-radius: 8px;
  width: 70%;
  max-width: 800px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  overflow-y: auto;
}