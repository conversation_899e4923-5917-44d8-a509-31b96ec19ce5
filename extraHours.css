/* تنسيقات خاصة بقسم الساعات الإضافية فقط - لا تتعارض مع التنسيق العام */

/* تنسيقات النموذج - متوافقة مع التنسيق العام */
.extra-hour-form .form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg, 1.5rem);
  margin-bottom: var(--spacing-xl, 2rem);
  padding: var(--spacing-xl, 2rem);
  background: var(--bg-light, #f8f9fa);
  border-radius: var(--border-radius-lg, 12px);
  border: 1px solid var(--border-light, #e9ecef);
}

.extra-hour-form .form-group.full-width {
  grid-column: 1 / -1;
}

.extra-hour-form .form-actions.full-width {
  grid-column: 1 / -1;
}

/* تنسيقات متجاوبة للنموذج */
@media (max-width: 1200px) {
  .extra-hour-form .form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .extra-hour-form .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .extra-hour-form .form-grid {
    grid-template-columns: 1fr;
  }
}

/* استخدام تنسيقات التبويبات من التنسيق العام - لا حاجة لتنسيقات إضافية */

/* تنسيقات خاصة بجداول الساعات الإضافية */
.added-extra-hours-container h3::before {
  content: '\f017';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  margin-left: 10px;
}

.extra-hours-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* تنسيق الصف المميز (آخر إضافة) */
.extra-hours-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.extra-hours-table tr:first-child td {
  font-weight: bold;
}

/* تنسيقات الرسائل الجميلة */
.success-message, .error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  min-width: 300px;
  max-width: 500px;
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
  font-family: 'Cairo', sans-serif;
}

.success-message {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-left: 5px solid #2e7d32;
}

.error-message {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  border-left: 5px solid #c62828;
}

.message-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.message-content i {
  font-size: 20px;
  margin-left: 12px;
  opacity: 0.9;
}

.message-content span {
  flex: 1;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* تحسين الرسائل للشاشات الصغيرة */
@media (max-width: 768px) {
  .success-message, .error-message {
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }

  .message-content {
    padding: 14px 16px;
    font-size: 13px;
  }

  .message-content i {
    font-size: 18px;
    margin-left: 10px;
  }
}

.extra-hours-table th,
.extra-hours-table td {
  padding: 12px;
  text-align: center;
  border-bottom: 1px solid #ddd;
}

.extra-hours-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.extra-hours-table tbody tr:hover {
  background: #f8f9fa;
}

/* تنسيقات نافذة التعديل */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 15px;
  left: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

/* تنسيق فلاتر البحث المحددة للساعات الإضافية */
.search-filters-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.filter-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.search-btn {
  background-color: #007bff;
  color: white;
}

.search-btn:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #6c757d;
  color: white;
}

.reset-btn:hover {
  background-color: #545b62;
}

/* تنسيق زر عرض التفاصيل */
.details-btn {
  background-color: #673AB7;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.3s ease;
}

.details-btn:hover {
  background-color: #5E35B1;
}

/* تنسيق النافذة المنبثقة لتفاصيل الساعات الإضافية */
#extraHoursDetailsModal .modal-content {
  max-width: 1200px;
  width: 90%;
  position: relative;
  min-height: 600px;
  padding: 30px;
}

/* تحسين تخطيط معلومات الموظف في النافذة الكبيرة */
#extraHoursDetailsModal #employeeExtraHoursInfo {
  margin-bottom: 25px;
}

#extraHoursDetailsModal #employeeExtraHoursInfo > p {
  display: inline-block;
  margin: 0 20px 10px 0;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #673AB7;
}

/* تنسيق بطاقات ملخص الساعات الإضافية */
#extraHoursDetailsModal .extra-hours-summary-info {
  margin-top: 20px;
}

#extraHoursDetailsModal .extra-hours-summary-info h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 18px;
  text-align: center;
}

.extra-hours-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.summary-card.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.summary-card.count {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.summary-card.average {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.summary-card-icon {
  font-size: 2.5em;
  margin-bottom: 10px;
  opacity: 0.9;
}

.summary-card-label {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.9;
  font-weight: 500;
}

.summary-card-value {
  font-size: 28px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* تنسيق جدول التفاصيل */
.details-table-container {
  margin-top: 25px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

.details-table th {
  background-color: #673AB7;
  color: white;
  padding: 12px;
  text-align: center;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.details-table td {
  padding: 10px 12px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.details-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.details-table tr:hover {
  background-color: #e3f2fd;
}

/* تنسيق أزرار الإجراءات */
.details-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  justify-content: flex-start;
}

.print-btn,
.export-details-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.print-btn:hover,
.export-details-btn:hover {
  background-color: #0b7dda;
}

.close-details-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.close-details-btn:hover {
  background-color: #da190b;
}

/* تنسيقات متجاوبة للنافذة المنبثقة */
@media (max-width: 768px) {
  #extraHoursDetailsModal .modal-content {
    width: 95%;
    padding: 20px;
    margin: 2% auto;
    max-height: 95vh;
  }

  .extra-hours-summary-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .summary-card {
    padding: 15px;
  }

  .summary-card-value {
    font-size: 24px;
  }

  .details-actions {
    flex-direction: column;
    gap: 10px;
  }

  .details-table-container {
    max-height: 300px;
  }
}
